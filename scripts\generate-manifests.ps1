#!/usr/bin/env pwsh
param(
    [Parameter(Mandatory=$true)]
    [string]$IssueBody,
    
    [Parameter(Mandatory=$false)]
    [string]$OutputDir = ".",
    
    [Parameter(Mandatory=$false)]
    [switch]$DryRun
)

# Function to extract value from issue body
function Get-IssueValue {
    param(
        [string]$Body,
        [string]$FieldName
    )
    
    $pattern = "### $FieldName\s*\n\s*(.+?)(?=\n###|\n\n|\Z)"
    $match = [regex]::Match($Body, $pattern, [System.Text.RegularExpressions.RegexOptions]::Singleline)
    
    if ($match.Success) {
        return $match.Groups[1].Value.Trim()
    }
    return ""
}

# Function to parse environment variables
function Parse-EnvVars {
    param([string]$EnvText)
    
    $envVars = @{}
    if ($EnvText) {
        $lines = $EnvText -split "`n"
        foreach ($line in $lines) {
            $line = $line.Trim()
            if ($line -and $line.Contains("=")) {
                $parts = $line -split "=", 2
                $envVars[$parts[0].Trim()] = $parts[1].Trim()
            }
        }
    }
    return $envVars
}

# Function to parse secret keys in KEY=VALUE format
function Parse-SecretKeys {
    param([string]$SecretText)

    $secrets = @{}
    if ($SecretText) {
        $lines = $SecretText -split "`n"
        foreach ($line in $lines) {
            $line = $line.Trim()
            if ($line -and $line.Contains("=")) {
                $parts = $line -split "=", 2
                if ($parts.Length -eq 2) {
                    $key = $parts[0].Trim()
                    $value = $parts[1].Trim()
                    if ($key -and $value) {
                        $secrets[$key] = $value
                    }
                }
            }
        }
    }
    return $secrets
}

# Function to validate inputs
function Validate-Inputs {
    param([hashtable]$Config)
    
    $errors = @()
    
    # Required fields validation
    $requiredFields = @('app_name', 'project_id', 'container_image', 'environment', 'replicas', 'cpu_request', 'cpu_limit', 'memory_request', 'memory_limit', 'container_port', 'service_type', 'app_type')
    
    foreach ($field in $requiredFields) {
        if (-not $Config[$field] -or $Config[$field] -eq "") {
            $errors += "Missing required field: $field"
        }
    }
    
    # Project ID validation (lowercase, alphanumeric, hyphens only)
    if ($Config.project_id -and $Config.project_id -notmatch '^[a-z0-9-]+$') {
        $errors += "Project ID must be lowercase alphanumeric with hyphens only"
    }
    
    # Resource validation
    if ($Config.cpu_request -and $Config.cpu_request -notmatch '^\d+m?$|^\d*\.?\d+$') {
        $errors += "Invalid CPU request format"
    }
    
    if ($Config.memory_request -and $Config.memory_request -notmatch '^\d+(Mi|Gi|Ki)$') {
        $errors += "Invalid memory request format"
    }
    
    # Port validation
    if ($Config.container_port -and ($Config.container_port -as [int]) -le 0) {
        $errors += "Container port must be a positive integer"
    }
    
    # NodePort validation
    if ($Config.node_port -and (($Config.node_port -as [int]) -lt 30000 -or ($Config.node_port -as [int]) -gt 32767)) {
        $errors += "NodePort must be between 30000 and 32767"
    }
    
    # Ingress validation
    if ($Config.enable_ingress -eq "true" -and (-not $Config.ingress_host -or $Config.ingress_host -eq "")) {
        $errors += "Ingress host is required when ingress is enabled"
    }
    
    # Database validation
    if ($Config.enable_database -eq "true" -and (-not $Config.db_name -or $Config.db_name -eq "")) {
        $errors += "Database name is required when database is enabled"
    }
    
    # PVC validation
    if ($Config.enable_pvc -eq "true") {
        if (-not $Config.pvc_size -or $Config.pvc_size -eq "") {
            $errors += "PVC size is required when PVC is enabled"
        }
        if (-not $Config.pvc_mount_path -or $Config.pvc_mount_path -eq "") {
            $errors += "PVC mount path is required when PVC is enabled"
        }
    }

    # Security fields validation (optional but format validation if provided)
    if ($Config.google_client_id -and $Config.google_client_id -ne "") {
        if ($Config.google_client_id -notmatch '^\d+-[a-zA-Z0-9]+\.apps\.googleusercontent\.com$') {
            $errors += "Google Client ID format is invalid (should be like: 123456789-abcdef.apps.googleusercontent.com)"
        }
    }

    if ($Config.google_client_secret -and $Config.google_client_secret -ne "") {
        if ($Config.google_client_secret -notmatch '^GOCSPX-[a-zA-Z0-9_-]+$') {
            $errors += "Google Client Secret format is invalid (should start with GOCSPX-)"
        }
    }

    if ($Config.smtp_port -and $Config.smtp_port -ne "") {
        if (($Config.smtp_port -as [int]) -le 0 -or ($Config.smtp_port -as [int]) -gt 65535) {
            $errors += "SMTP port must be between 1 and 65535"
        }
    }

    return $errors
}

# Function to replace template variables
function Replace-TemplateVars {
    param(
        [string]$Template,
        [hashtable]$Config
    )
    
    $result = $Template
    
    # Simple variable replacement
    foreach ($key in $Config.Keys) {
        $placeholder = "{{" + $key.ToUpper() + "}}"
        $result = $result -replace [regex]::Escape($placeholder), $Config[$key]
    }
    
    # Handle conditional blocks with else (process multiple times for nested conditions)
    for ($i = 0; $i -lt 10; $i++) {
        $hasChanges = $false
        
        # Handle {{#if VARIABLE}} blocks
        $ifPattern = '\{\{#if\s+(\w+)\}\}(.*?)\{\{/if\}\}'
        $ifMatches = [regex]::Matches($result, $ifPattern, [System.Text.RegularExpressions.RegexOptions]::Singleline)
        
        foreach ($match in $ifMatches) {
            $variable = $match.Groups[1].Value
            $content = $match.Groups[2].Value
            $fullMatch = $match.Groups[0].Value
            
            if ($Config[$variable] -eq "true" -or $Config[$variable.ToLower()] -eq "true") {
                $result = $result -replace [regex]::Escape($fullMatch), $content
                $hasChanges = $true
            } else {
                $result = $result -replace [regex]::Escape($fullMatch), ""
                $hasChanges = $true
            }
        }
        
        # Handle {{#eq VARIABLE "value"}} blocks
        $eqPattern = '\{\{#eq\s+(\w+)\s+"([^"]+)"\}\}(.*?)\{\{/eq\}\}'
        $eqMatches = [regex]::Matches($result, $eqPattern, [System.Text.RegularExpressions.RegexOptions]::Singleline)
        
        foreach ($match in $eqMatches) {
            $variable = $match.Groups[1].Value
            $value = $match.Groups[2].Value
            $content = $match.Groups[3].Value
            $fullMatch = $match.Groups[0].Value
            
            if ($Config[$variable] -eq $value -or $Config[$variable.ToLower()] -eq $value) {
                $result = $result -replace [regex]::Escape($fullMatch), $content
                $hasChanges = $true
            } else {
                $result = $result -replace [regex]::Escape($fullMatch), ""
                $hasChanges = $true
            }
        }
        
        if (-not $hasChanges) {
            break
        }
    }
    
    # Clean up any remaining template artifacts
    $result = $result -replace '\{\{else\}\}', ''
    $result = $result -replace '\{\{/eq\}\}', ''
    $result = $result -replace '\{\{/if\}\}', ''
    
    return $result
}

Write-Host "🚀 Starting manifest generation..." -ForegroundColor Green

# Set error action preference for better error handling
$ErrorActionPreference = "Stop"

# Parse issue body
Write-Host "📋 Parsing issue data..." -ForegroundColor Yellow
Write-Host "📋 Issue body length: $($IssueBody.Length) characters" -ForegroundColor Cyan

$config = @{
    # Basic Application Configuration
    app_name = Get-IssueValue -Body $IssueBody -FieldName "Application Name"
    project_id = Get-IssueValue -Body $IssueBody -FieldName "Project Identifier"
    container_image = Get-IssueValue -Body $IssueBody -FieldName "Container Image"
    environment = Get-IssueValue -Body $IssueBody -FieldName "Environment"
    namespace = Get-IssueValue -Body $IssueBody -FieldName "Kubernetes Namespace"
    replicas = Get-IssueValue -Body $IssueBody -FieldName "Replica Count"

    # Resource Configuration
    cpu_request = Get-IssueValue -Body $IssueBody -FieldName "CPU Request"
    cpu_limit = Get-IssueValue -Body $IssueBody -FieldName "CPU Limit"
    memory_request = Get-IssueValue -Body $IssueBody -FieldName "Memory Request"
    memory_limit = Get-IssueValue -Body $IssueBody -FieldName "Memory Limit"
    container_port = Get-IssueValue -Body $IssueBody -FieldName "Container Port"

    # Service & Ingress Configuration
    service_type = Get-IssueValue -Body $IssueBody -FieldName "Service Type"
    node_port = Get-IssueValue -Body $IssueBody -FieldName "NodePort (if applicable)"
    enable_ingress = if ((Get-IssueValue -Body $IssueBody -FieldName "Enable Ingress") -match "- \[x\]") { "true" } else { "false" }
    ingress_host = Get-IssueValue -Body $IssueBody -FieldName "Ingress Host"
    ingress_path = Get-IssueValue -Body $IssueBody -FieldName "Ingress Path"

    # Database Configuration
    enable_database = if ((Get-IssueValue -Body $IssueBody -FieldName "Enable PostgreSQL Database") -match "- \[x\]") { "true" } else { "false" }
    db_name = Get-IssueValue -Body $IssueBody -FieldName "Database Name"
    db_user = Get-IssueValue -Body $IssueBody -FieldName "Database User"
    storage_size = Get-IssueValue -Body $IssueBody -FieldName "Storage Size"

    # Storage Configuration
    enable_pvc = if ((Get-IssueValue -Body $IssueBody -FieldName "Enable Persistent Volume") -match "- \[x\]") { "true" } else { "false" }
    pvc_size = Get-IssueValue -Body $IssueBody -FieldName "PVC Size"
    pvc_mount_path = Get-IssueValue -Body $IssueBody -FieldName "PVC Mount Path"

    # Application Type & Health
    app_type = Get-IssueValue -Body $IssueBody -FieldName "Application Type"
    health_check_path = Get-IssueValue -Body $IssueBody -FieldName "Health Check Path"

    # NestJS Application Configuration
    app_url = Get-IssueValue -Body $IssueBody -FieldName "Application URL"
    api_url = Get-IssueValue -Body $IssueBody -FieldName "API URL"
    cors_origins = Get-IssueValue -Body $IssueBody -FieldName "CORS Allowed Origins"
    jwt_expiration = Get-IssueValue -Body $IssueBody -FieldName "JWT Token Expiration"

    # Additional Application Configuration
    app_version = Get-IssueValue -Body $IssueBody -FieldName "Application Version"
    public_url = Get-IssueValue -Body $IssueBody -FieldName "Public URL"

    # SMTP Configuration
    smtp_host = Get-IssueValue -Body $IssueBody -FieldName "SMTP Host"
    smtp_port = Get-IssueValue -Body $IssueBody -FieldName "SMTP Port"
    smtp_from = Get-IssueValue -Body $IssueBody -FieldName "SMTP From Address"

    # Authentication & Security Configuration (will be populated from Additional Secret Keys)
    jwt_secret = ""
    db_password = ""
    smtp_user = ""
    smtp_pass = ""
    google_client_id = ""
    google_client_secret = ""

    # OAuth2 Configuration
    google_redirect_uri = Get-IssueValue -Body $IssueBody -FieldName "Google OAuth Redirect URI"
    oauth_scopes = Get-IssueValue -Body $IssueBody -FieldName "OAuth2 Scopes"
    oauth_redirect_uris = Get-IssueValue -Body $IssueBody -FieldName "Authorized Redirect URIs"

    # JVM Configuration (for Spring Boot applications)
    java_xms = Get-IssueValue -Body $IssueBody -FieldName "JVM Initial Heap Size"
    java_xmx = Get-IssueValue -Body $IssueBody -FieldName "JVM Maximum Heap Size"
}

# Set defaults
if (-not $config.namespace -or $config.namespace -eq "") {
    $config.namespace = $config.project_id
}

# Set application type-specific database defaults
switch ($config.app_type) {
    "react-frontend" {
        # React frontends typically don't need databases
        if (-not $config.enable_database -or $config.enable_database -eq "") {
            $config.enable_database = "false"
        }
    }
    "springboot-backend" {
        # Spring Boot backends often need databases
        if (-not $config.enable_database -or $config.enable_database -eq "") {
            $config.enable_database = "true"
        }
    }
    default {
        # Keep existing behavior for other types
        if (-not $config.enable_database -or $config.enable_database -eq "") {
            $config.enable_database = "false"
        }
    }
}

if (-not $config.db_user -or $config.db_user -eq "") {
    $config.db_user = "postgres"
}

# Add NODE_ENV conditional flags for DB_HOST logic
$config.node_env_dev = if ($config.environment -eq "dev") { "true" } else { "false" }
$config.node_env_staging = if ($config.environment -eq "staging") { "true" } else { "false" }
$config.node_env_prod = if ($config.environment -eq "prod") { "true" } else { "false" }

if (-not $config.db_name -or $config.db_name -eq "") {
    $config.db_name = "userauth"
}

# Set default DB_HOST based on environment if not specified
if (-not $config.db_host -or $config.db_host -eq "") {
    switch ($config.environment) {
        "dev" { $config.db_host = "localhost" }
        "staging" { $config.db_host = "$($config.project_id)-postgres.staging" }
        "production" { $config.db_host = "$($config.project_id)-postgres.production" }
        default { $config.db_host = "localhost" }
    }
}

if (-not $config.ingress_path -or $config.ingress_path -eq "") {
    $config.ingress_path = "/"
}

if (-not $config.storage_size -or $config.storage_size -eq "") {
    $config.storage_size = "5Gi"
}

# Set application type-specific defaults
switch ($config.app_type) {
    "react-frontend" {
        if (-not $config.container_port -or $config.container_port -eq "") {
            $config.container_port = "80"
        }
        if (-not $config.memory_request -or $config.memory_request -eq "") {
            $config.memory_request = "128Mi"
        }
        if (-not $config.memory_limit -or $config.memory_limit -eq "") {
            $config.memory_limit = "256Mi"
        }
        if (-not $config.cpu_request -or $config.cpu_request -eq "") {
            $config.cpu_request = "100m"
        }
        if (-not $config.cpu_limit -or $config.cpu_limit -eq "") {
            $config.cpu_limit = "200m"
        }
        if (-not $config.health_check_path -or $config.health_check_path -eq "") {
            $config.health_check_path = "/"
        }
    }
    "springboot-backend" {
        if (-not $config.container_port -or $config.container_port -eq "") {
            $config.container_port = "8080"
        }
        if (-not $config.memory_request -or $config.memory_request -eq "") {
            $config.memory_request = "512Mi"
        }
        if (-not $config.memory_limit -or $config.memory_limit -eq "") {
            $config.memory_limit = "1Gi"
        }
        if (-not $config.cpu_request -or $config.cpu_request -eq "") {
            $config.cpu_request = "250m"
        }
        if (-not $config.cpu_limit -or $config.cpu_limit -eq "") {
            $config.cpu_limit = "500m"
        }
        if (-not $config.health_check_path -or $config.health_check_path -eq "") {
            $config.health_check_path = "/actuator/health"
        }
    }
    default {
        # Default values for other application types
        if (-not $config.container_port -or $config.container_port -eq "") {
            $config.container_port = "8080"
        }
        if (-not $config.memory_request -or $config.memory_request -eq "") {
            $config.memory_request = "256Mi"
        }
        if (-not $config.memory_limit -or $config.memory_limit -eq "") {
            $config.memory_limit = "512Mi"
        }
        if (-not $config.cpu_request -or $config.cpu_request -eq "") {
            $config.cpu_request = "250m"
        }
        if (-not $config.cpu_limit -or $config.cpu_limit -eq "") {
            $config.cpu_limit = "500m"
        }
        if (-not $config.health_check_path -or $config.health_check_path -eq "") {
            $config.health_check_path = "/health"
        }
    }
}

# Set application type-specific configuration defaults
switch ($config.app_type) {
    "react-frontend" {
        if (-not $config.api_url -or $config.api_url -eq "") {
            $config.api_url = "http://localhost:8080"
        }
        if (-not $config.app_url -or $config.app_url -eq "") {
            $config.app_url = "http://localhost:$($config.container_port)"
        }
        if (-not $config.public_url -or $config.public_url -eq "") {
            $config.public_url = "http://localhost:$($config.container_port)"
        }
        # React apps don't typically need CORS/JWT config (handled by backend)
    }
    "springboot-backend" {
        if (-not $config.app_url -or $config.app_url -eq "") {
            $config.app_url = "http://localhost:3000"
        }
        if (-not $config.api_url -or $config.api_url -eq "") {
            $config.api_url = "http://localhost:$($config.container_port)"
        }
        if (-not $config.cors_origins -or $config.cors_origins -eq "") {
            $config.cors_origins = "http://localhost:3000,http://localhost:3001"
        }
        if (-not $config.jwt_expiration -or $config.jwt_expiration -eq "") {
            $config.jwt_expiration = "86400000"
        }
        if (-not $config.public_url -or $config.public_url -eq "") {
            $config.public_url = "http://localhost:3000"
        }
    }
    default {
        # Default configuration for other application types
        if (-not $config.app_url -or $config.app_url -eq "") {
            $config.app_url = "http://localhost:3000"
        }
        if (-not $config.api_url -or $config.api_url -eq "") {
            $config.api_url = "http://localhost:3000"
        }
        if (-not $config.cors_origins -or $config.cors_origins -eq "") {
            $config.cors_origins = "http://localhost:3000,http://localhost:3001"
        }
        if (-not $config.jwt_expiration -or $config.jwt_expiration -eq "") {
            $config.jwt_expiration = "86400000"
        }
        if (-not $config.public_url -or $config.public_url -eq "") {
            $config.public_url = "http://localhost:3000"
        }
    }
}

# Set default for app_version if not provided
if (-not $config.app_version -or $config.app_version -eq "") {
    $config.app_version = "1.0.0"
}

# Set defaults for JVM configuration
if (-not $config.java_xms -or $config.java_xms -eq "") {
    $config.java_xms = "256m"
}

if (-not $config.java_xmx -or $config.java_xmx -eq "") {
    $config.java_xmx = "512m"
}

# Set defaults for SMTP configuration
if (-not $config.smtp_host -or $config.smtp_host -eq "") {
    $config.smtp_host = "smtp.gmail.com"
}

if (-not $config.smtp_port -or $config.smtp_port -eq "") {
    $config.smtp_port = "587"
}

if (-not $config.smtp_from -or $config.smtp_from -eq "") {
    $config.smtp_from = '"No Reply" <<EMAIL>>'
}

# Set defaults for OAuth2 configuration
if (-not $config.google_redirect_uri -or $config.google_redirect_uri -eq "") {
    $config.google_redirect_uri = "http://localhost:$($config.container_port)/oauth2/callback/google"
}

if (-not $config.oauth_scopes -or $config.oauth_scopes -eq "") {
    $config.oauth_scopes = "email,profile,openid"
}

# Parse environment variables and secrets
$envVarsText = Get-IssueValue -Body $IssueBody -FieldName "Additional Environment Variables"
$config.env_vars = Parse-EnvVars -EnvText $envVarsText

$secretKeysText = Get-IssueValue -Body $IssueBody -FieldName "Additional Secret Keys"
$config.secret_keys = Parse-SecretKeys -SecretText $secretKeysText

# Extract specific secret values from the parsed secret keys
if ($config.secret_keys.ContainsKey("JWT_SECRET")) {
    $config.jwt_secret = $config.secret_keys["JWT_SECRET"]
}
if ($config.secret_keys.ContainsKey("DB_PASSWORD")) {
    $config.db_password = $config.secret_keys["DB_PASSWORD"]
}
if ($config.secret_keys.ContainsKey("SMTP_USER")) {
    $config.smtp_user = $config.secret_keys["SMTP_USER"]
}
if ($config.secret_keys.ContainsKey("SMTP_PASS")) {
    $config.smtp_pass = $config.secret_keys["SMTP_PASS"]
}
if ($config.secret_keys.ContainsKey("GOOGLE_CLIENT_ID")) {
    $config.google_client_id = $config.secret_keys["GOOGLE_CLIENT_ID"]
}
if ($config.secret_keys.ContainsKey("GOOGLE_CLIENT_SECRET")) {
    $config.google_client_secret = $config.secret_keys["GOOGLE_CLIENT_SECRET"]
}

# Handle base64 encoding for secret values
if ($config.jwt_secret -and $config.jwt_secret -ne "") {
    $config.jwt_secret_b64 = [Convert]::ToBase64String([Text.Encoding]::UTF8.GetBytes($config.jwt_secret))
} else {
    $config.jwt_secret_b64 = "UExBQ0VIT0xERVI="  # PLACEHOLDER
}

if ($config.db_password -and $config.db_password -ne "") {
    $config.db_password_b64 = [Convert]::ToBase64String([Text.Encoding]::UTF8.GetBytes($config.db_password))
} else {
    $config.db_password_b64 = "UExBQ0VIT0xERVI="  # PLACEHOLDER
}

if ($config.smtp_user -and $config.smtp_user -ne "") {
    $config.smtp_user_b64 = [Convert]::ToBase64String([Text.Encoding]::UTF8.GetBytes($config.smtp_user))
} else {
    $config.smtp_user_b64 = "UExBQ0VIT0xERVI="  # PLACEHOLDER
}

if ($config.smtp_pass -and $config.smtp_pass -ne "") {
    $config.smtp_pass_b64 = [Convert]::ToBase64String([Text.Encoding]::UTF8.GetBytes($config.smtp_pass))
} else {
    $config.smtp_pass_b64 = "UExBQ0VIT0xERVI="  # PLACEHOLDER
}

if ($config.google_client_id -and $config.google_client_id -ne "") {
    $config.google_client_id_b64 = [Convert]::ToBase64String([Text.Encoding]::UTF8.GetBytes($config.google_client_id))
} else {
    $config.google_client_id_b64 = "UExBQ0VIT0xERVI="  # PLACEHOLDER
}

if ($config.google_client_secret -and $config.google_client_secret -ne "") {
    $config.google_client_secret_b64 = [Convert]::ToBase64String([Text.Encoding]::UTF8.GetBytes($config.google_client_secret))
} else {
    $config.google_client_secret_b64 = "UExBQ0VIT0xERVI="  # PLACEHOLDER
}

# Process OAuth redirect URIs
if ($config.oauth_redirect_uris -and $config.oauth_redirect_uris -ne "") {
    # Convert multi-line input to comma-separated string
    $config.oauth_redirect_uris = ($config.oauth_redirect_uris -split "`n" | ForEach-Object { $_.Trim() } | Where-Object { $_ -ne "" }) -join ","
} else {
    # Set default OAuth redirect URIs
    $config.oauth_redirect_uris = "http://localhost:$($config.container_port)/oauth2/callback/google,http://localhost:3000/oauth2/redirect,myandroidapp://oauth2/redirect,myiosapp://oauth2/redirect"
}

# Add database user to secrets if database is enabled
if ($config.enable_database -eq "true") {
    $config.db_user_b64 = [Convert]::ToBase64String([Text.Encoding]::UTF8.GetBytes($config.db_user))
}

Write-Host "✅ Issue data parsed successfully" -ForegroundColor Green

# Validate inputs
Write-Host "🔍 Validating inputs..." -ForegroundColor Yellow
$validationErrors = Validate-Inputs -Config $config

if ($validationErrors.Count -gt 0) {
    Write-Host "❌ Validation failed:" -ForegroundColor Red
    foreach ($error in $validationErrors) {
        Write-Host "  - $error" -ForegroundColor Red
    }
    exit 1
}

Write-Host "✅ Validation passed" -ForegroundColor Green

# Check if project already exists
$projectDir = Join-Path $OutputDir $config.project_id
if (Test-Path $projectDir) {
    Write-Host "⚠️  Project directory already exists: $projectDir" -ForegroundColor Yellow
    if (-not $DryRun) {
        $response = Read-Host "Do you want to overwrite? (y/N)"
        if ($response -ne "y" -and $response -ne "Y") {
            Write-Host "❌ Aborted" -ForegroundColor Red
            exit 1
        }
    }
}

if ($DryRun) {
    Write-Host "🔍 DRY RUN - No files will be created" -ForegroundColor Cyan
    Write-Host "Would create project: $($config.project_id)" -ForegroundColor Cyan
    Write-Host "Configuration:" -ForegroundColor Cyan
    $config | ConvertTo-Json -Depth 3 | Write-Host
    exit 0
}

# Create project directories
Write-Host "📁 Creating project structure..." -ForegroundColor Yellow
$argoCdDir = Join-Path $projectDir "argocd"
$k8sDir = Join-Path $projectDir "k8s"

New-Item -ItemType Directory -Path $argoCdDir -Force | Out-Null
New-Item -ItemType Directory -Path $k8sDir -Force | Out-Null

Write-Host "✅ Project directories created" -ForegroundColor Green

Write-Host "📝 Generating manifests..." -ForegroundColor Yellow

# Generate ArgoCD manifests
$templates = @(
    @{ Source = "templates/argocd/application.yaml"; Target = "$argoCdDir/application.yaml" },
    @{ Source = "templates/argocd/project.yaml"; Target = "$argoCdDir/project.yaml" },
    @{ Source = "templates/k8s/namespace.yaml"; Target = "$k8sDir/namespace.yaml" },
    @{ Source = "templates/k8s/configmap.yaml"; Target = "$k8sDir/configmap.yaml" },
    @{ Source = "templates/k8s/secret.yaml"; Target = "$k8sDir/secret.yaml" },
    @{ Source = "templates/k8s/deployment.yaml"; Target = "$k8sDir/deployment.yaml" },
    @{ Source = "templates/k8s/service.yaml"; Target = "$k8sDir/service.yaml" }
)

# Conditional templates

if ($config.enable_ingress -eq "true") {
    $templates += @{ Source = "templates/k8s/ingress.yaml"; Target = "$k8sDir/ingress.yaml" }
}

if ($config.enable_database -eq "true") {
    $templates += @(
        @{ Source = "templates/k8s/postgres-deployment.yaml"; Target = "$k8sDir/postgres-deployment.yaml" },
        @{ Source = "templates/k8s/postgres-service.yaml"; Target = "$k8sDir/postgres-service.yaml" },
        @{ Source = "templates/k8s/postgres-pvc.yaml"; Target = "$k8sDir/postgres-pvc.yaml" }
    )
}

if ($config.enable_pvc -eq "true") {
    $templates += @{ Source = "templates/k8s/pvc.yaml"; Target = "$k8sDir/pvc.yaml" }
}

# Process templates
Write-Host "🔄 Processing templates..." -ForegroundColor Blue

$successCount = 0
$totalTemplates = $templates.Count

foreach ($template in $templates) {
    Write-Host "� Processing: $($template.Source)" -ForegroundColor Cyan
    
    try {
        if (Test-Path $template.Source) {
            $content = Get-Content $template.Source -Raw -Encoding UTF8
            Write-Host "  📄 Template size: $($content.Length) characters" -ForegroundColor Gray

            $processedContent = Replace-TemplateVars -Template $content -Config $config
            Write-Host "  🔄 Processed size: $($processedContent.Length) characters" -ForegroundColor Gray

            # Remove empty lines and clean up
            $processedContent = $processedContent -replace '(?m)^\s*$\n', ''
            $processedContent = $processedContent.Trim()

            # Ensure target directory exists
            $targetDir = Split-Path $template.Target -Parent
            if (-not (Test-Path $targetDir)) {
                New-Item -ItemType Directory -Path $targetDir -Force | Out-Null
                Write-Host "  📁 Created directory: $targetDir" -ForegroundColor Yellow
            }

            # Write processed content
            Set-Content -Path $template.Target -Value $processedContent -Encoding UTF8
            Write-Host "  ✅ Generated: $($template.Target)" -ForegroundColor Green
            $successCount++
        } else {
            Write-Host "  ❌ Template not found: $($template.Source)" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "  ❌ Error processing template: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n📊 Processing Summary:" -ForegroundColor Blue
Write-Host "  ✅ Successfully processed: $successCount/$totalTemplates templates" -ForegroundColor Green

if ($successCount -eq $totalTemplates) {
    Write-Host "🎉 Manifest generation completed successfully!" -ForegroundColor Green
} else {
    Write-Host "⚠️  Manifest generation completed with warnings" -ForegroundColor Yellow
    exit 1
}

Write-Host "📁 Project created at: $projectDir" -ForegroundColor Cyan
Write-Host "🚀 Next steps:" -ForegroundColor Cyan
Write-Host "  1. Review generated manifests" -ForegroundColor White
Write-Host "  2. Update secret values in $k8sDir/secret.yaml" -ForegroundColor White
Write-Host "  3. Deploy with: kubectl apply -f $argoCdDir/application.yaml" -ForegroundColor White

# Add missing process_template_file function
function Process-TemplateFile {
    param(
        [string]$SourcePath,
        [string]$DestinationPath,
        [hashtable]$TemplateVars
    )
    
    try {
        if (-not (Test-Path $SourcePath)) {
            Write-Host "  ❌ Template file not found: $SourcePath" -ForegroundColor Red
            return $false
        }
        
        $content = Get-Content $SourcePath -Raw -Encoding UTF8
        $processedContent = Replace-TemplateVars -Template $content -Config $TemplateVars
        
        # Ensure target directory exists
        $targetDir = Split-Path $DestinationPath -Parent
        if (-not (Test-Path $targetDir)) {
            New-Item -ItemType Directory -Path $targetDir -Force | Out-Null
        }
        
        Set-Content -Path $DestinationPath -Value $processedContent -Encoding UTF8
        Write-Host "  ✅ Generated: $DestinationPath" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "  ❌ Error processing template $SourcePath`: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Update the function call to use the correct PowerShell function name
function Copy-AndProcessK8sTemplates {
    param(
        [hashtable]$TemplateVars,
        [string]$OverlaysPath,
        [string]$BasePath
    )
    
    $environment = $TemplateVars['environment']
    
    # Copy base templates
    $baseTemplates = @(
        'deployment.yaml',
        'service.yaml',
        'configmap.yaml',
        'kustomization.yaml'
    )
    
    foreach ($templateFile in $baseTemplates) {
        $srcPath = "templates/k8s/base/$templateFile"
        if (Test-Path $srcPath) {
            $dstPath = Join-Path $BasePath $templateFile
            Process-TemplateFile -SourcePath $srcPath -DestinationPath $dstPath -TemplateVars $TemplateVars
        }
    }
    
    # Copy overlay templates
    $overlayTemplates = @(
        'kustomization.yaml',
        'deployment-patch.yaml',
        'service-patch.yaml'
    )
    
    foreach ($templateFile in $overlayTemplates) {
        $srcPath = "templates/k8s/overlays/$environment/$templateFile"
        if (Test-Path $srcPath) {
            $dstPath = Join-Path $OverlaysPath $templateFile
            Process-TemplateFile -SourcePath $srcPath -DestinationPath $dstPath -TemplateVars $TemplateVars
        }
    }
}








