#!/usr/bin/env python3
"""
Validate ArgoCD application paths and structure
"""

import os
import sys
import yaml
from pathlib import Path

def validate_argocd_paths(project_id, environment):
    """Validate that ArgoCD application paths will exist after generation"""
    
    expected_path = f"{project_id}/k8s/overlays/{environment}"
    base_path = f"{project_id}/k8s/base"
    
    print(f"🔍 Validating ArgoCD paths for {project_id} in {environment}")
    
    # Check if overlay path exists
    if not os.path.exists(expected_path):
        print(f"❌ Error: ArgoCD path {expected_path} does not exist")
        return False
    
    # Check if base path exists
    if not os.path.exists(base_path):
        print(f"❌ Error: Base path {base_path} does not exist")
        return False
    
    # Check required files
    required_files = [
        f"{expected_path}/kustomization.yaml",
        f"{base_path}/deployment.yaml",
        f"{base_path}/service.yaml",
        f"{base_path}/kustomization.yaml"
    ]
    
    for file_path in required_files:
        if not os.path.exists(file_path):
            print(f"❌ Error: Required file {file_path} missing")
            return False
        
        # Validate YAML syntax
        try:
            with open(file_path, 'r') as f:
                yaml.safe_load(f)
            print(f"✅ Valid: {file_path}")
        except yaml.YAMLError as e:
            print(f"❌ YAML Error in {file_path}: {e}")
            return False
    
    print(f"✅ All ArgoCD paths and files validated for {project_id}")
    return True

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python validate-argocd-paths.py <project_id> <environment>")
        sys.exit(1)
    
    project_id = sys.argv[1]
    environment = sys.argv[2]
    
    if validate_argocd_paths(project_id, environment):
        print("🎉 Validation passed!")
        sys.exit(0)
    else:
        print("💥 Validation failed!")
        sys.exit(1)