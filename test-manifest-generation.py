#!/usr/bin/env python3
"""
Test script to verify manifest generation fixes
"""

import os
import sys
import subprocess
import tempfile
import shutil

def test_python_script_syntax():
    """Test if the Python script has valid syntax"""
    print("🔍 Testing Python script syntax...")
    try:
        result = subprocess.run([
            sys.executable, '-m', 'py_compile', 'scripts/generate-manifests-cicd.py'
        ], capture_output=True, text=True, cwd='.')
        
        if result.returncode == 0:
            print("✅ Python script syntax is valid")
            return True
        else:
            print(f"❌ Python script syntax error: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Error testing Python script: {e}")
        return False

def test_template_files_exist():
    """Test if all required template files exist"""
    print("🔍 Testing template file existence...")
    
    required_templates = [
        'templates/argocd/application.yaml',
        'templates/argocd/project.yaml',
        'templates/k8s/base/deployment-rolling.yaml',
        'templates/k8s/base/service.yaml',
        'templates/k8s/base/configmap.yaml',
        'templates/k8s/base/secret.yaml',
        'templates/k8s/base/resourcequota.yaml',
        'templates/k8s/base/kustomization.yaml'
    ]
    
    missing_files = []
    for template in required_templates:
        if not os.path.exists(template):
            missing_files.append(template)
        else:
            print(f"✅ Found: {template}")
    
    if missing_files:
        print("❌ Missing template files:")
        for file in missing_files:
            print(f"  - {file}")
        return False
    else:
        print("✅ All required template files exist")
        return True

def test_python_script_imports():
    """Test if the Python script can be imported without errors"""
    print("🔍 Testing Python script imports...")
    try:
        # Add scripts directory to path
        sys.path.insert(0, 'scripts')
        
        # Try to import the module
        import importlib.util
        spec = importlib.util.spec_from_file_location("generate_manifests_cicd", "scripts/generate-manifests-cicd.py")
        module = importlib.util.module_from_spec(spec)
        
        # This will test if all imports work
        spec.loader.exec_module(module)
        
        print("✅ Python script imports successfully")
        return True
    except Exception as e:
        print(f"❌ Python script import error: {e}")
        return False
    finally:
        # Remove from path
        if 'scripts' in sys.path:
            sys.path.remove('scripts')

def test_template_validation():
    """Test template validation"""
    print("🔍 Running template validation...")
    try:
        result = subprocess.run([
            sys.executable, 'scripts/validate-templates.py'
        ], capture_output=True, text=True, cwd='.')
        
        if result.returncode == 0:
            print("✅ Template validation passed")
            # Check for any errors in output
            if "Total errors: 0" in result.stdout:
                print("✅ No template errors found")
                return True
            else:
                print("⚠️ Template validation completed with warnings")
                return True
        else:
            print(f"❌ Template validation failed: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Error running template validation: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Testing GitOps Manifest Generation Fixes")
    print("=" * 50)
    
    tests = [
        test_python_script_syntax,
        test_template_files_exist,
        test_python_script_imports,
        test_template_validation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()  # Add spacing between tests
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            print()
    
    print("=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The fixes should resolve the manifest generation issues.")
        return 0
    else:
        print("⚠️ Some tests failed. Please review the output above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
